@import url('https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
*{
    margin: 0;
    padding:0;
    box-sizing: border-box;
    font-family:'Prompt', sans-serif;
}

body{
    background: rgb(155,154,157);
    background: radial-gradient(circle, #9b9a9d 0%, rgba(148,228,233,1) 100%);
    display:flex;
    flex-direction: column;
}
nav{
    background: #242526;
    width: 100%;
    z-index: 999;
    position: fixed;
    height: 70px;
    
}

nav .wrapper{
    width: 90%;
    padding: 0 30px;
    display: flex;
    margin: auto;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    
  
}
.wrapper .logo a{
    color: #fff;
    text-decoration: none;
    font-size: 30px;
    font-weight: 600;
    
}
.wrapper .logo a span{
    display: table-cell;
    margin: 0;
    padding: 0;
}
.wrapper .nav-links{
    display: inline-flex;
    
}
.nav-links li{
    list-style: none;
    
}
.nav-links li a{
    color:#fff;
    text-decoration: none;
    font-size: 24px;
    font-weight: 500;
    padding: 10px 30px;
    border-radius: 10px;
    transition: all .3s ease;
}
.nav-links li a:hover{
    
    box-shadow: 0 0 10px #cc00ffd2, 0 0 25px #cc00ffd2,0 0 50px #cc00ffd2;
}

main{
    top: 70px;
    width: 90%;
    margin: 0 5%;
    background: rgb(238,174,202);
    background: radial-gradient(circle, rgba(238,174,202,1) 0%, rgba(148,187,233,1) 100%);
    min-height: 100%;
    display:grid;
    grid-template-columns: repeat(1, 300px 1fr);
    grid-template-rows: repeat(1, 100vh);
    position: fixed; 
    
}
aside{
    background:  #615264;
    width: 100%;

}

.fa-plus{
    position: absolute;
    right: 20px;
    line-height: 70px;
    color: #fff;

}
.fa-minus{
    position: absolute;
    right: 20px;
    line-height: 70px;
    color: #fff;
    display: none;
}
.one.change1{
    display: inline;

}
.frd.change1{
    display: none;
}
.two.change1{
    display: inline;
}
.scd.change1{
    display: none;
}
aside ul{
    width: 100%;
}
aside ul li {
    list-style: none;
    line-height: 70px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
aside ul li:first-child{
    border-top: 1px solid rgba(255, 255, 255, 0.1) ;
}
aside ul li a{
    text-decoration: none;
    font-size: 22px;
    font-weight: bold;
    color: #eeedf3;
    display: block;
    padding-left: 25px;
    width: 100%;
    position: relative;
}
aside ul li a:hover{
    color: cyan;
    background: radial-gradient(circle, #858499 0%, #ad94e9 100%);
}

aside ul ul{
    position: static;
    display: none;
}
aside ul .stucture-show1.show{
    display: block;
}
aside ul .stucture-show2.show1{
    display: block;
}

aside ul ul li{
    line-height: 60px;

}
aside ul ul li a{
    font-size: 20px;
    padding-left: 60px;
}
article{
    padding: 30px 30px 60px 30px;
    height:100%;
    overflow: scroll;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
article h1{
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: 3rem;
    color: #8f7aec;

}
/* article > div{
    position: relative;
    right: -200%;
} */
article img{
    top: 50px;
    position: inherit;
    width: 100%;
    height: auto;
}
article h2{
    padding-top: 30px;
}
article p{
    line-height: 30px;
    font-size: 1.3rem;
}
span{
    line-height: 50px;
}
.active {
    top:0px;
}

@media only screen and (max-width:1279px){
    .wrapper .logo a{
        color: #fff;
        text-decoration: none;
        font-size: 25px;
        font-weight: 600;
    }
    .nav-links li a{
        color:#fff;
        text-decoration: none;
        font-size: 18px;
        font-weight: 500;
        padding: 10px 30px;
        border-radius: 10px;
        transition: all .3s ease;
    }
    article h1{
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-size: 1.5rem;
        color: #8f7aec;
    
    }
    article h2{
        padding-top: 30px;
        font-size: 1.2rem;
    }
    article p{
        line-height: 30px;
        font-size: 1rem;
    }
    aside ul li a{
        text-decoration: none;
        font-size: 20px;
        font-weight: 500;
        color: #eeedf3;
        display: block;
        padding-left: 25px;
        width: 100%;
        position: relative;
    }
    aside ul ul li a{
        font-size: 18px;
        padding-left: 60px;
    }
}

@media only screen and (max-width:1082px){
    .wrapper .logo a{
        color: #fff;
        text-decoration: none;
        font-size: 20px;
        font-weight: 600;
    }
    .nav-links li a{
        color:#fff;
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        padding: 5px 15px;
        border-radius: 5px;
        transition: all .3s ease;
    }
    article h1{
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-size: 1.2rem;
        color: #8f7aec;
    
    }
    article h2{
        padding-top: 30px;
        font-size: 1rem;
    }
    article p{
        line-height: 25px;
        font-size: .8rem;
    }
    aside ul li a{
        text-decoration: none;
        font-size: 17px;
        font-weight: 500;
        color: #eeedf3;
        display: block;
        padding-left: 25px;
        width: 100%;
        position: relative;
    }
    aside ul ul li a{
        font-size: 15px;
        padding-left: 60px;
    }
}