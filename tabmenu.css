@import url('https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
*{
    margin: 0;
    padding:0;
    box-sizing: border-box;
    font-family:'Prompt', sans-serif;
}

body{
    background: rgb(155,154,157);
    background: radial-gradient(circle, #9b9a9d 0%, rgba(148,228,233,1) 100%);
    display:flex;
    flex-direction: column;
}
nav{
    background: #242526;
    width: 100%;
    z-index: 999;
    position: fixed;
    height: 70px;

}

nav .wrapper{
    width: 90%;
    max-width: 1200px;
    padding: 0 20px;
    display: flex;
    margin: auto;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    position: relative;
}
.wrapper .logo a{
    color: #fff;
    text-decoration: none;
    font-size: clamp(20px, 4vw, 30px);
    font-weight: 600;
    white-space: nowrap;
}
.wrapper .logo a span{
    display: table-cell;
    margin: 0;
    padding: 0;
}
.wrapper .nav-links{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.nav-links li{
    list-style: none;

}
.nav-links li a{
    color:#fff;
    text-decoration: none;
    font-size: clamp(14px, 2.5vw, 24px);
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 10px;
    transition: all .3s ease;
    white-space: nowrap;
}
.nav-links li a:hover{

    box-shadow: 0 0 10px #cc00ffd2, 0 0 25px #cc00ffd2,0 0 50px #cc00ffd2;
}

main{
    top: 70px;
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    background: rgb(238,174,202);
    background: radial-gradient(circle, rgba(238,174,202,1) 0%, rgba(148,187,233,1) 100%);
    min-height: calc(100vh - 70px);
    display: grid;
    grid-template-columns: minmax(250px, 300px) 1fr;
    grid-template-rows: 1fr;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
}
aside{
    background:  #615264;
    width: 100%;

}

.fa-plus{
    position: absolute;
    right: 20px;
    line-height: 70px;
    color: #fff;

}
.fa-minus{
    position: absolute;
    right: 20px;
    line-height: 70px;
    color: #fff;
    display: none;
}
.one.change1{
    display: inline;

}
.frd.change1{
    display: none;
}
.two.change1{
    display: inline;
}
.scd.change1{
    display: none;
}
aside ul{
    width: 100%;
}
aside ul li {
    list-style: none;
    line-height: 70px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
aside ul li:first-child{
    border-top: 1px solid rgba(255, 255, 255, 0.1) ;
}
aside ul li a{
    text-decoration: none;
    font-size: 22px;
    font-weight: bold;
    color: #eeedf3;
    display: block;
    padding-left: 25px;
    width: 100%;
    position: relative;
}
aside ul li a:hover{
    color: cyan;
    background: radial-gradient(circle, #858499 0%, #ad94e9 100%);
}

aside ul ul{
    position: static;
    display: none;
}
aside ul .stucture-show1.show{
    display: block;
}
aside ul .stucture-show2.show1{
    display: block;
}

aside ul ul li{
    line-height: 60px;

}
aside ul ul li a{
    font-size: 20px;
    padding-left: 60px;
}
article{
    padding: 30px 30px 60px 30px;
    height:100%;
    overflow: scroll;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
article h1{
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: 3rem;
    color: #8f7aec;

}
/* article > div{
    position: relative;
    right: -200%;
} */
article img{
    top: 50px;
    position: inherit;
    width: 100%;
    height: auto;
}
article h2{
    padding-top: 30px;
}
article p{
    line-height: 30px;
    font-size: 1.3rem;
}
span{
    line-height: 50px;
}
.active {
    top:0px;
}

/* Tablet and iPad Landscape (1025px - 1440px) */
@media only screen and (min-width: 1025px) and (max-width: 1440px){
    nav .wrapper{
        width: 95%;
        padding: 0 25px;
    }
    .wrapper .logo a{
        font-size: 28px;
    }
    .nav-links li a{
        font-size: 20px;
        padding: 8px 20px;
    }
    main{
        width: 95%;
        grid-template-columns: minmax(280px, 320px) 1fr;
    }
    article h1{
        font-size: 2.5rem;
    }
    article h2{
        font-size: 1.4rem;
    }
    article p{
        font-size: 1.2rem;
    }
}

/* iPad Portrait and Small Laptops (769px - 1024px) */
@media only screen and (min-width: 769px) and (max-width: 1024px){
    nav .wrapper{
        width: 95%;
        padding: 0 20px;
    }
    .wrapper .logo a{
        font-size: 24px;
    }
    .nav-links li a{
        font-size: 16px;
        padding: 6px 12px;
    }
    main{
        width: 95%;
        grid-template-columns: minmax(250px, 280px) 1fr;
    }
    article{
        padding: 20px;
    }
    article h1{
        font-size: 2rem;
    }
    article h2{
        font-size: 1.3rem;
        padding-top: 25px;
    }
    article p{
        font-size: 1.1rem;
        line-height: 28px;
    }
    aside ul li a{
        font-size: 18px;
        padding-left: 20px;
    }
    aside ul ul li a{
        font-size: 16px;
        padding-left: 50px;
    }
}

/* Small Tablets and Large Phones (481px - 768px) */
@media only screen and (min-width: 481px) and (max-width: 768px){
    nav .wrapper{
        width: 98%;
        padding: 0 15px;
        flex-direction: column;
        height: auto;
        min-height: 70px;
        padding: 10px 15px;
    }
    nav{
        height: auto;
        min-height: 70px;
    }
    .wrapper .logo a{
        font-size: 22px;
        margin-bottom: 10px;
    }
    .wrapper .nav-links{
        flex-direction: column;
        width: 100%;
        gap: 5px;
    }
    .nav-links li{
        width: 100%;
        text-align: center;
    }
    .nav-links li a{
        font-size: 14px;
        padding: 8px 10px;
        display: block;
        width: 100%;
    }
    main{
        top: auto;
        margin-top: 120px;
        width: 98%;
        position: relative;
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        min-height: calc(100vh - 120px);
        transform: none;
        left: auto;
    }
    aside{
        order: 1;
    }
    article{
        order: 2;
        padding: 15px;
    }
    article h1{
        font-size: 1.8rem;
        position: static;
        transform: none;
        top: auto;
        left: auto;
        margin: 20px 0;
    }
    article h2{
        font-size: 1.2rem;
        padding-top: 20px;
    }
    article p{
        font-size: 1rem;
        line-height: 26px;
    }
    aside ul li a{
        font-size: 16px;
        padding-left: 20px;
    }
    aside ul ul li a{
        font-size: 14px;
        padding-left: 40px;
    }
}

/* Mobile Phones (480px and below) */
@media only screen and (max-width: 480px){
    nav .wrapper{
        width: 100%;
        padding: 8px 10px;
        flex-direction: column;
        height: auto;
        min-height: 60px;
    }
    nav{
        height: auto;
        min-height: 60px;
    }
    .wrapper .logo a{
        font-size: 18px;
        margin-bottom: 8px;
    }
    .wrapper .nav-links{
        flex-direction: column;
        width: 100%;
        gap: 3px;
    }
    .nav-links li{
        width: 100%;
        text-align: center;
    }
    .nav-links li a{
        font-size: 12px;
        padding: 6px 8px;
        display: block;
        width: 100%;
    }
    main{
        top: auto;
        margin-top: 100px;
        width: 100%;
        position: relative;
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        min-height: calc(100vh - 100px);
        transform: none;
        left: auto;
    }
    aside{
        order: 1;
    }
    article{
        order: 2;
        padding: 10px;
    }
    article h1{
        font-size: 1.4rem;
        position: static;
        transform: none;
        top: auto;
        left: auto;
        margin: 15px 0;
    }
    article h2{
        font-size: 1rem;
        padding-top: 15px;
    }
    article p{
        font-size: 0.9rem;
        line-height: 24px;
    }
    aside ul li a{
        font-size: 14px;
        padding-left: 15px;
        line-height: 50px;
    }
    aside ul ul li a{
        font-size: 12px;
        padding-left: 30px;
        line-height: 45px;
    }
    aside ul li{
        line-height: 50px;
    }
    aside ul ul li{
        line-height: 45px;
    }
}