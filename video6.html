﻿<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]>      <html class="no-js"> <!--<![endif]-->
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>วิดีโอ</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="">
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Prompt&display=swap');

            *{
                margin: 0;
                padding:0;
                font-family: 'Prompt', sans-serif;
            }

           body{
                background: rgb(174,204,238);
                background: radial-gradient(circle, rgba(174,204,238,1) 0%, rgba(148,233,219,1) 100%);
                display: flex;
                flex-direction: column;
                min-height: 100vh;
            }
            .video{
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 5vw;
                
            }
            .counter{
                display: flex;
                flex-direction: column;
                align-items: center; 
                margin: 5vw;
                border: 2px solid #000;
                padding: 1vw;
            }
            h1{
                margin-bottom: 1vw;
                text-align: center;
                font-size: 1.5vw;
            }
            h2{
                margin: 2vw 0 3vw 0;
                font-size: 1.3vw;
            }
             h2 a{
                text-decoration: none;
                color: #3505e2;
             }
             h2 a:hover{
                background-color: rgb(5, 109, 5);
                padding: 10px;
                border-radius: 5px;
                color: #FFFFFF;
             }

            h3{
                font-size: 1.6vw;
            }
            p{
                font-size: 1.5vw;
                margin: 1vw 0 1vw 0;
            }
        </style> 
    </head>
    <body>
        <!--[if lt IE 7]>
            <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="#">upgrade your browser</a> to improve your experience.</p>
        <![endif]-->
                

        <div class="video">
            <h1>อบรมหลักสูตร “คณะกรรมการการจัดซื้อจัดจ้างตามพระราชบัญญัติ การจัดซื้อจัดจ้างและการบริหารพัสดุภาครัฐ และระเบียบกระทรวงการคลัง <br> ว่าด้วยการจัดซื้อจัดจ้างและการบริหารพัสดุภาครัฐ พ.ศ. 2560” เมื่อวันที่ 19 พฤศจิกายน 2567</h1>
            <video width="50%" controls controlsList="nodownload" oncontextmenu="return false;" id="my-video-player" src="video/อบรม 2024-11-19.mp4"></video>
        </div> 
           
        <!-- <div class="counter">
            <p>มีผู้เข้าชมคลิปวีดีโอ</p>
            <h3 class="counterthree"></h3>
            <p>ครั้ง</p>
        </div>


        <script>
            var counterContainer = document.querySelector(".counterthree");
            var visitCount = localStorage.getItem("page_view02");
            
            // Check if page_view entry is present
            if (visitCount) {
                visitCount = Number(visitCount) + 1;
            localStorage.setItem("page_view02", visitCount);
            } else {
              visitCount = 1;
              localStorage.setItem("page_view02", 1);
            }
            counterContainer.innerHTML = visitCount;
           
        </script> -->

    </body>
</html>