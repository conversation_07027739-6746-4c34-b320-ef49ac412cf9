@import url('https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
*{
    margin: 0;
    padding:0;
    /* box-sizing: border-box; */
    font-family:'Prompt', sans-serif;
}
body{
    background: rgb(155,154,157);
    background: radial-gradient(circle, rgba(155,154,157,1) 0%, rgba(148,228,233,1) 100%);
    display: flex;
    flex-direction: column;

}
nav{
    background: #242526;
    width: 100%;
    z-index: 999;
    position: fixed;
    height: 70px;
    
}

nav .wrapper{
    width: 90%;
    padding: 0 30px;
    display: flex;
    margin: auto;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    
  
}
.wrapper .logo a{
    color: #fff;
    text-decoration: none;
    font-size: 30px;
    font-weight: 600;
    
}
.wrapper .logo a span{
    display: table-cell;
    margin: 0;
    padding: 0;
}
.wrapper .nav-links{
    display: inline-flex;
    
}
.nav-links li{
    list-style: none;
    
}
.nav-links li a{
    color:#fff;
    text-decoration: none;
    font-size: 24px;
    font-weight: 500;
    padding: 10px 30px;
    border-radius: 10px;
    transition: all .3s ease;
}
.nav-links li a:hover{
    
    box-shadow: 0 0 10px #cc00ffd2, 0 0 25px #cc00ffd2,0 0 50px #cc00ffd2;
}
header{
    width: 90%;
    min-height: 520px;
    top: 70px;
    margin: auto;
    background: rgb(181,174,238);
    background: radial-gradient(circle, rgba(181,174,238,1) 0%, rgba(148,228,233,1) 100%);
    color: #fff;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    position: relative; 
       
}
header h1{
    font-size: 70px;
    color: #2d6e1d;
    
}
header p{
    font-size: 40px;
    color: #2d6e1d;
    margin-bottom: 30px;
  
}
section{
    width: 90%;
    top: 70px;
    margin: auto;
    /* padding: 0px 50px; */
    background: rgb(238,174,202);
    background: radial-gradient(circle, rgba(238,174,202,1) 0%, rgba(148,187,233,1) 100%);
    min-height: 700px;
    position: relative; 
    /* display: flex;
    flex-wrap: wrap; */
    overflow: hidden;

}
.banner-container{
    position: relative; 
    min-height: 700px;
    max-width: 100%;
    margin: 20px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 200px));
    grid-template-rows: repeat(auto-fit, minmax(200px, 200px)); 
    grid-gap: 50px ;
    justify-content: center;
    align-content: center;
}
.banner-list{
    background-color: #fff;
    text-align: center;
    font-size: 16px;
    box-sizing: border-box;
    border-radius: 40px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}
.banner-list a{
    text-decoration: none;
}
.banner-list i{
    font-size: 3rem;
}
.banner-list p{
    margin-top: 25px ;
}
/* .box-1, .box-2, .box-3, .box-4, .box-5, .box-6, .box-7, .box-8, .box-9, .box-10{
    width: 100px;
    height: 100px;
    background-color: #fff;
    border:1px solid #000;
} */
/* footer */
footer{
    top: 70px;
    background: #1b1b1b;
    width: 100%;
    height: auto;
    padding-top: 40px;
    color: #fff;
    position: relative; 
}
.footer-content{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
}
.footer-content h2{
    font-size: 1.8rem;
    font-weight: 500;
    text-transform: capitalize;
    line-height: 3rem;
}
.footer-content p{
    margin: 10px auto;
    line-height: 15px;
    font-size: 20px;

}
.socials{
    list-style: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem 0 3rem 0;
    
}
.socials li{
    margin:0 7px;
    border-radius: 100%;
    height: 50px;
    width: 50px;
    background: #171515;
    translate: 1s;
    box-sizing: border-box;
} 
.socials li:hover{
    border: 2px solid rgba(255, 51, 230, .5);
    
}
.socials a{
    text-decoration: none;
    color: #fff;
        
}
.socials a i{
    font-size: 1.2rem;
    margin: 15px;
}

.footer-bottom{
    background: #000;
    width: 100%;
    padding: 20px 0;
    text-align: center;
}
.footer-bottom p{
    font-size: 16px;

}
.footer-bottom span{
    text-transform: uppercase;
    opacity: .3;
    font-weight: 400;
}




/* Reponsive Web site */
@media only screen and (max-width:1230px){
    .wrapper .logo a{
        color: #fff;
        text-decoration: none;
        font-size: 25px;
        font-weight: 600;
    }
    .nav-links li a{
        color:#fff;
        text-decoration: none;
        font-size: 18px;
        font-weight: 500;
        padding: 10px 30px;
        border-radius: 10px;
        transition: all .3s ease;
    }
    header{
        width: 90%;
        min-height: 400px;
        top: 70px;
        margin: auto;
        background: rgb(181,174,238);
        background: radial-gradient(circle, rgba(181,174,238,1) 0%, rgba(148,228,233,1) 100%);
        color: #fff;
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        flex-direction: column;
        text-align: center;
        position: relative; 
    }
    .banner-container{
        position: relative; 
        min-height: 700px;
        max-width: 100%;
        margin: 20px 20px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 150px));
        grid-template-rows: repeat(auto-fit, minmax(150px, 150px)); 
        grid-gap: 30px ;
        justify-content: center;
        align-content: center;
    }
    .banner-list{
        background-color: #fff;
        text-align: center;
        font-size: 10px;
        box-sizing: border-box;
        border-radius: 40px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

@media only screen and (max-width:1035px){
    .wrapper .logo a{
        color: #fff;
        text-decoration: none;
        font-size: 20px;
        font-weight: 600;
    }
    .nav-links li a{
        color:#fff;
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        padding: 5px 15px;
        border-radius: 5px;
        transition: all .3s ease;
    }
    header h1{
        font-size: 50px;
        color: #2d6e1d;
        
    }
    header p{
        font-size: 25px;
        color: #2d6e1d;
        margin-bottom: 30px;
      
    }
}

@media only screen and (max-width: 1720px) {

  .wrapper .logo a {
    font-size: 25px;
    font-weight: 600;
  }

  .nav-links li a {
    font-size: 20px;
    font-weight: 500;
    padding: 10px 20px;

  }
}
@media only screen and (max-width: 1500px) {

  .wrapper .logo a {
    font-size: 23px;
    font-weight: 600;
  }

  .nav-links li a {
    font-weight: 500;
    font-size: 18px;
    padding: 10px 15px;

  } 
}
@media only screen and (max-width: 1300px) {

  .wrapper .logo a {
    font-size: 23px;
    font-weight: 600;
  }

  .nav-links li a {
    font-size: 18px;
    padding: 10px 10px;
    font-weight: 500;
  }
}