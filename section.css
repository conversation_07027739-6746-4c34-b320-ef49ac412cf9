@import url('https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
*{
    margin: 0;
    padding:0;
    box-sizing: border-box;
    font-family:'Prompt', sans-serif;
}
body{
    background: rgb(155,154,157);
    background: radial-gradient(circle, rgba(155,154,157,1) 0%, rgba(148,228,233,1) 100%);
    display:flex;
    flex-direction: column;
    min-height: 100vh;

}
nav{
    background: #242526;
    width: 100%;
    z-index: 999;
    position: fixed;
    height: 70px;
    
}

nav .wrapper{
    width: 90%;
    padding: 0 30px;
    display: flex;
    margin: auto;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    
  
}
.wrapper .logo a{
    color: #fff;
    text-decoration: none;
    font-size: 30px;
    font-weight: 600;
    
}
.wrapper .logo a span{
    display: table-cell;
    margin: 0;
    padding: 0;
}
.wrapper .nav-links{
    display: inline-flex;
    
}
.nav-links li{
    list-style: none;
    
}
.nav-links li a{
    color:#fff;
    text-decoration: none;
    font-size: 24px;
    font-weight: 500;
    padding: 10px 30px;
    border-radius: 10px;
    transition: all .3s ease;
}
.nav-links li a:hover{
    
    box-shadow: 0 0 10px #cc00ffd2, 0 0 25px #cc00ffd2,0 0 50px #cc00ffd2;
}

main{
    top: 70px;
    padding-top: 30px;
    background: rgb(238,174,202);
    background: radial-gradient(circle, rgba(238,174,202,1) 0%, rgba(148,187,233,1) 100%);
    min-height: 100vh;
    /* display: grid;
    position: relative;
    grid-template-columns: repeat(auto-fit, 220px);
    grid-column-gap:20px;
    justify-content: center; */
    align-items: center;
}
.struction{
    width: 100%;
    height: auto;
    position: relative;
    text-align: center;

}

.struction ul{
    padding-top: 50px ;
    position: relative;
    transition: .5s;
    text-align: center;
}
.struction li{
    display: inline-table;
    text-align: center;
    list-style-type: none;
    position: relative;
    padding: 10px;
    transition: .5s;
}
.fig{
    display: inline-table;
    padding-top: 0px;
}

.struction li:before, .struction li:after{
    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    border-top: 5px solid rgb(140, 141, 145);
    width: 51%;
    height: 20px;
}
.struction li:after{
    right: auto;
    left: 50%;
    border-left: 5px solid rgb(140, 141, 145);
}
.struction li:only-child:after, .struction li:only-child:before{
display: none;
}
.struction li:only-child{
    padding-top: 0;
}
.struction li:first-child:before, .struction li:last-child:after{
    border: 0 none;
}
.struction li:last-child:before{
    border-right: 5px solid rgb(140, 141, 145);
    border-radius: 0px 5px 0px 0px;
}
.struction li:last-child:after{
    border-radius: 5px 0px 0px 0px;
}
.struction ul ul:before{
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    border-left: 5px solid rgb(140, 141, 145);
    width: 0;
    height: 50px;
}
figure{
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
img{
    width: 120px;
    height: auto;
}
figcaption{
    font-size: 11px;
    text-align: center;
    margin: 30px 0;
    width: 200px;
}
@media only screen and (max-width: 1720px) {

  .wrapper .logo a {
    font-size: 25px;
    font-weight: 600;
  }

  .nav-links li a {
    font-size: 20px;
    font-weight: 500;
    padding: 10px 20px;

  }
}
@media only screen and (max-width: 1500px) {

  .wrapper .logo a {
    font-size: 23px;
    font-weight: 600;
  }

  .nav-links li a {
    font-weight: 500;
    font-size: 18px;
    padding: 10px 15px;

  } 
}
@media only screen and (max-width: 1300px) {

  .wrapper .logo a {
    font-size: 23px;
    font-weight: 600;
  }

  .nav-links li a {
    font-size: 18px;
    padding: 10px 10px;
    font-weight: 500;
  }
}