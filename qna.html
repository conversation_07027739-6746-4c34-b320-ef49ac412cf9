<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ถาม-ตอบ</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .highlight {
            background-color: rgba(253, 186, 116, 0.5); /* orange-300 with 50% opacity */
            transition: background-color 0.3s;
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <div class="w-full max-w-lg bg-white shadow-lg rounded-lg overflow-hidden h-[550px] xl:h-[750px]">
        <h1 class="px-4 py-3 bg-green-500 text-white font-bold text-center">Q&A</h1>
        <div id="chat-box" class="px-4 py-3 space-y-3 overflow-y-auto h-[420px] xl:h-[630px]">
            <!-- Chat messages will be dynamically inserted here -->
        </div>
        <div class="border-t border-gray-200 px-3 py-5">
            <input id="search-input" type="text" placeholder="Search chat history..."
                   class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
    </div>

    <script>
        const qaData = [
            { question: "ระบบนี้ใช้ลงข้อมูลการซื้อจ้างวงเงินต่ำกว่า 100,000.- ด้วยมั้ยคะ", answer: "ลงด้วยค่ะ" },
            { question: "แล้วถ้า หผ.ทำประกาศเอง?", answer: "สามารถทำได้ค่ะ" },
            { question: "วงเงิน 100.000 ขึ้นไป ที่ต้อง eGP หรือทุกวงเงินคับ", answer: "เงื่อนไขโครงการที่ต้องนำมาลงข้อมูลจะเป็นตามเว็บม่วงเดิม (https://bidding.pea.co.th/) เลยค่ะ" },
            { question: "ถ้าพนักงานคนที่1 เป็นคนลงแผน ตอนแก้ สามารถเป็นพนักงานคนอื่น(คนที่2,3)แก้ไขเปลี่ยนแปลงแผนได้มั้ยคะ", answer: "กรณีอยู่กองเดียวกันสามารถช่วยกันทำงานได้ค่ะ" },
            { question: "มีรายละเอียด ขั้นตอนในการทำงานโปรแกรมนี้ไหมครับ", answer: "สามารถศึกษาเพิ่มเติมได้ผ่านเมนู \"คู่มือการใช้งานระบบ\"" },
            { question: "ประกาศนี้เป็นแบบต่ำกว่าและมากกว่า 100,000 ใช่ไหมคะ หรือเฉพาะมากกว่า 100,000 คะ", answer: "สามารถลงข้อมูลได้ทั้ง 2 กรณีเลยค่ะ โดยสำหรับต่ำแสน จะไปลงที่เมนูเบอร์ 7 และ 8 ค่ะ" },
            { question: "จำนวนเต็มหรือทศนิยมจะถูกใช้ไปจนจบงานเลยหรือเปล่าคะ", answer: "ใช่ค่ะ จะเป็นลักษณะเดียวกับในระบบ e-gp ที่นำไปใช้ควบคุมการแสดงผลของจำนวนพัสดุว่าจะแสดงเป็น 1 หรือ 1.0000 ค่ะ" },
            { question: "พอจะมี flow ไหมคะ ว่าในแต่ละขั้นตอน แผนกที่รับผิดชอบในการลงข้อมูลเป็นแผนกไหนค่ะ", answer: "ไม่มี flow ครับ สามารถดำเนินการลงประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ) ตามคู่มือการใช้งานได้ (อยู่แท็บด้านบนของระบบ) ทั้งนี้เมื่อมีปัญหาการใช้งาน สามารถแจ้งได้ที่มุมด้านขวาล่างของระบบครับ" },
            { question: "มีข้อสงสัยค่ะ ถ้านายAทำแผนจัดซื้อจัดจ้าง แล้วนายBทำประกาศร่างTOR ในกรณีนี้นายฺBสามารถลิ้งเอกสารกับแผนที่นายAทำไว้ได้มั้ยคะ", answer: "สามารถลิ้งกันได้ค่ะ โดยนาย A และนาย B ต้องอยู่กองเดียวกัน (ซึ่งมาจากเงื่อนไขที่อยู่กองเดียวกันจะช่วยกันทำงานได้)" },
            { question: "ลงควบคู่โปรแกรมเก่านานแค่ไหนนะคะที่จะทดลอง คือหน้างานงานเยอะมากนะคะ ถ้าวันหนึ่งลง100รายการ ลง2ที่ เป็น200รายการ มันเยอะมากนะคะ ถ้าทดลอง6เดือน ตายแน่ๆค่ะ", answer: "การลงคู่ขนาน 2 ระบบ จะให้ทำในช่วงเดือน พฤษภาคม และ มิถุนายน เพื่อให้ผู้ใช้งานได้ทดลองใช้และติดตาม feedback ต่างๆ และเมื่อถึงวันที่ 1 กค. เราจะทำการปิดระบบเดิม และใช้เพียงแค่ระบบใหม่เท่านั้น\n\nแต่ในส่วนโครงการที่มีการเริ่มลงไว้ที่เว็บม่วงแต่ยังประกาศไม่ครบถ้วนทุกขั้นตอน จะยังให้ลงที่เว็บเก่าแบบเดิมจนครบทุกขั้นตอน" },
            { question: "เหมือนจะเพิ่มงาน เป็น 3 ระบบไหมคะ จากเดิม ทำในระบบ e-GP และ ทำนอกระบบ (พิมพ์เอกสารในเวิร์ด) แลัวยังต้องมาลงข้อมูลแบบเดิมเหมือนใน e-GP ในระบบ e-Pro อีก ใช่ไหมคะ ?", answer: "ระบบนี้สุดท้ายแล้ว จะเป็นการทดแทนระบบเดิม (https://bidding.pea.co.th/) มีเพียงแค่ 2 เดือนแรกเท่านั้น (พฤษภาคม และ มิถุนายน) ที่เราจะทำการลงคู่ขนาน 2 ระบบ" },
            { question: "สามารถดึงข้อมูลจาก Sap และ กรมบัญชีกลางมาใช้ เพื่อลงข้อมูลน้อยลงได้หรือเปล่าคะ", answer: "การดึงข้อมูลจากระบบ e-gp ในเบื้องต้นเราจะทำการใช้เฉพาะกองจัดหาส่วนกลางก่อนนะคะ ในส่วนของเขตต่างจังหวัด ระบบใหม่จะให้เป็นการลงข้อมูลในลักษณะที่คล้ายกับเว็บม่วงก่อนค่ะ" },
            { question: "ถ้าใช้เว็บนี้แล้ว เว็บม่วงไม่ต้องทำแล้วใช่มั๊ยค่ะ", answer: "ในช่วงเดือน พฤษภาคม และ มิถุนายน จะเป็นการลงข้อมูลทั้งระบบใหม่และเว็บม่วงนะคะ เพื่อให้ผู้ใช้งานได้ทดลองใช้และติดตาม feedback ต่างๆ และเมื่อถึงวันที่ 1 กค. เราจะทำการปิดระบบเดิม และใช้เพียงแค่ระบบใหม่ระบบเดียวเท่านั้น\n\nแต่ในส่วนโครงการที่มีการเริ่มลงไว้ที่เว็บม่วงแต่ยังประกาศไม่ครบถ้วนทุกขั้นตอน จะยังให้ลงที่เว็บเก่าแบบเดิมจนครบทุกขั้นตอน" },
            { question: "ทำคู่ขนาน 2 เดือน ไม่น่าจะพอนะคะ น่าจะต้องให้ กฟข. แต่ละเขต จัดประชุมชี้แจงแนวทางการดำเนินงานเพื่อให้เป็นไปในทิศทางเดียวกันอ่ะค่ะ", answer: "ถ้าไม่พอจะขยายให้อีกครับ ทั้งนี้ ฝวห. จะสำรวจโครงการที่ยังติดค้างก่อนวันที่ 1 ก.ค. 2568 เพื่อประเมินดูอีกครั้งคับ" },
            { question: "เหมือนกระบวนการจะเพิ่มกว่าเดิม", answer: "ณ ปัจจุบัน ระบบใหม่ที่เกิดขึ้นจะเป็นการลงข้อมูลในลักษณะเดิมแบบเว็บม่วง (https://bidding.pea.co.th/) โดยจะมีการกรอกข้อมูลที่ละเอียดขึ้นเพื่อให้สามารถนำข้อมูลไปต่อยอดเป็นรายงานต่างๆได้มากขึ้นค่ะ" },
            { question: "แล้วจะอบรมใช้งานจริงให้ผู้ปฏิบัติหน้างานจริงๆ เมื่อไหร่คะ", answer: "เริ่มใช้งานจริงได้ตั้งแต่วันที่ 6 พ.ค. 2568 สามารถศึกษาคู่มือการใช้งานได้ (อยู่แท็บด้านบนของระบบ) ทั้งนี้เมื่อมีปัญหาการใช้งาน สามารถแจ้งได้ที่มุมด้านขวาล่างของระบบครับ" },
            { question: "วันที่ประกาศ ระบบล็อคต้องเป็นปัจจุบันไหม สามารถลงวันที่ย้อนหลัง หรืออนาคตได้ไหม", answer: "ระบบให้เลือกย้อนหลังได้ค่ะ แต่ไม่สามารถเลือกวันอนาคตได้ แต่ทั้งนี้ทั้งนั้นทางเราขอให้การลงข้อมูลประกาศเป็นไปตามวันที่ประกาศในระบบ e-gp เพื่อให้ข้อมูลสอดคล้องกันทุกระบบ" },
            { question: "ข้อมูลที่เอามาใส่ เป็นข้อมูลที่ได้จาก e-gp ถูกมั้ยคะ", answer: "ถูกต้องค่ะ" },
            { question: "กฎกระทรวงใช้คำว่า \"ไม่เกิน 1 แสนบาท\" ครับ ไม่ใช่ต่ำกว่า 1 แสนบาท\" 2 คำนี้ความหมายไม่น่าจะเหมือนกันมั้บครับ", answer: "ดำเนินการปรับให้เรียบร้อยแล้วนะคะ" },
            { question: "ถ้าผู้บริหารอนุมัติประกาศในpaperแล้ว ในe-Pro พนักงานคลิ๊กอนุมัติเลยได้หรือไม่ หรือต้องสอนผู้บริหารเข้าคลิ๊กคะ", answer: "ในระบบจะต้องใช้เป็น User ผู้บริหารในการกดอนุมัติเพื่อประกาศค่ะ" },
            { question: "ระบบใหม่นี้สามารถดึงข้อมูลจาก E-GP ได้มั้ยครับ", answer: "การดึงข้อมูลจากระบบ e-gp ในเบื้องต้นเราจะทำการใช้เฉพาะกองจัดหาส่วนกลางก่อนนะคะ ในส่วนของเขตต่างจังหวัด ระบบใหม่จะให้เป็นการลงข้อมูลในลักษณะที่คล้ายกับเว็บม่วงก่อนค่ะ" },
            { question: "จะมีจัดอบรมระบบอีกครั้งไหมครับ", answer: "ไม่มีครับ สามารถศึกษาคู่มือการใช้งานได้ (อยู่แท็บด้านบนของระบบ) ทั้งนี้เมื่อมีปัญหาการใช้งาน สามารถแจ้งได้ที่มุมด้านขวาล่างของระบบครับ" },
            { question: "ถ้ามีจัดอบรมแยกไม่ควรเรียกแค่คนทำจัดซื้อนะคะ ควรเรียกทุกส่วนที่เกี่ยวข้อง แผนกอื่น รวมไปถึงคนอนุมัติด้วยค่ะ ได้โปรด มันเยอะค่ะ แล้วยากที่จะเข้าใจสำหรับบางคน", answer: "การอบรมอีกครั้ง ฝวห. จะรับพิจารณา ถ้าจะลงข้อมูลประกาศจัดซื้อจัดจ้าง สามารถศึกษาคู่มือการใช้งานได้ (อยู่แท็บด้านบนของระบบ) ทั้งนี้เมื่อมีปัญหาการใช้งาน สามารถแจ้งได้ที่มุมด้านขวาล่างของระบบครับ" },
            { question: "มีนำเสนออีกครั่งมั้ยครับ บรรยายเร็วมาก", answer: "ไม่มีครับ สามารถดูวีดิโอย้อนหลังได้ครับ เดี๋ยวจะมีการแจ้งเวียนให้ทราบอีกครั้งครับ" },
            { question: "ขั้นตอนการบันทึกผู้เสนอราคาและผลการตรวจสอบ คุณสมบัติ พอจะออกแบบให้สามารถ Upload Template เข้าไปได้ไหมคะ", answer: "สำหรับการ Upload Template จะมีการปรับระบบต่อไปในอนาคตนะคะ" },
            { question: "ราคาที่ใส่ในขั้นตอนที่ 6 - ราคาของผู้ชนะ เป็นราคาที่ต่อรองยังครับ", answer: "ราคาในช่องแรกจะเป็นราคาที่ผู้ประกอบการเสนอมาค่ะ แต่จะมีราคาในช่องถัดไปให้กรอกซึ่งจะหมายถึงราคาที่ต่อรองแล้ว/ราคาที่ตกลงซื้อขายค่ะ" },
            { question: "เกรงว่า ทำคู่ขนานกันไป จะเป็นการเพิ่มภาระงาน และระยะเวลาในแต่ละขั้นตอน เนื่องจากต้องพิมพ์ข้อมูลและรายละเอียดต่างๆ มากกว่า 1 ระบบ ไหมคะ ซึ่ง 1 โครงการ กว่าจะดำเนินการแต่ละขั้นตอนก็ใช้เวลาเยอะนะคะ พอจะมีแนวทางให้ประหยัดเวลาและลดขั้นตอนจากเดิมไหมคะ ? หรือทางส่วนกลางนำร่องทำคู่ขนานไปก่อน หากใช้เวลาไม่มาก และไม่เพิ่มภาระงาน เมื่อระบบลงตัวแล้ว ค่อยให้หน้างานปรับใช้ โดยลดขั้นตอนพิมพ์รายละเอียดนอกระบบ เป็นพิมพ์ผ่านระบบ แล้วเสนอคู่กับ e-GP เท่านี้ แทนดีไหมคะ", answer: "ต้องรบกวนใช้ทั้งสองระบบด้วยนะครับ ซึ่งใช้งานแค่ในส่วนของการลงประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ) โดยเริ่มจากโครงการที่ยังไม่ได้ลงประกาศในระบบเดิม ทั้งนี้ สามารถศึกษาคู่มือการใช้งานได้ (อยู่แท็บด้านบนของระบบ) ทั้งนี้เมื่อมีปัญหาการใช้งาน สามารถแจ้งได้ที่มุมด้านขวาล่างของระบบครับ" },
            { question: "ซื้อพัสดุ 70 รายการ มีผู้เสนอราคาอีกไม่น้อยกว่า 20 ราย ถ้าต้องบันทึกข้อมูลแบบนี้น่าจะเพิ่มภาระมากค่ะ เป็นไปได้ไหมว่าจะทำ Tamplate ให้นำเข้าข้อมูลให้เรียบร้อยก่อน ค่อยเปิดใช้งานค่ะ", answer: "สำหรับการ Upload Template จะมีการปรับระบบต่อไปในอนาคตนะคะ" },
            { question: "ถ้ากรอกข้อมูลผิด สามารถแก้ไข ได้ไหมคะ😄", answer: "กรณีข้อมูลผิด และส่งไปยังหัวหน้างานแล้ว แต่หัวหน้างานยังไม่พิจารณา สามารถให้หัวหน้างานส่งคืนแก้ไขได้ค่ะ\n\nแต่กรณีหัวหน้างานอนุมัติประกาศไปแล้ว จะมีเพียง User หัวหน้างานเท่านั้นที่สามารถเข้าแก้ไขข้อมูลได้ค่ะ" },
            { question: "มีแคสัญญา ใช่ไหมคะ มีใบสั่งซื้อด้วยไหมคะ กรณีที่ใช้ใบสั่งซื้อ ไม่ได้ใช้สัญญา ค่ะ", answer: "มีใบสั่งซื้อด้วยค่ะ" },
            { question: "ใน e-GP ต้องมีการบันทึกเบิกจ่ายเหมือนกัน ลิ้งค์ข้อมูลมาเลยได้ไหมคะ", answer: "ในเบื้องต้นยังไม่มีการดึงข้อมูลลงมานะคะ แต่อาจจะต้องพูดคุยเพิ่มเติมเพื่อปรับระบบต่อในเฟสถัดไปค่ะ" },
            { question: "ต่ำแสนเวปม่วงเก่าต้องทำไหมคะ", answer: "การลงคู่ขนาน 2 ระบบ จะให้ทำในช่วงเดือน พฤษภาคม และ มิถุนายน เพื่อให้ผู้ใช้งานได้ทดลองใช้และติดตาม feedback ต่างๆ และเมื่อถึงวันที่ 1 กค. เราจะทำการปิดระบบเดิม และใช้เพียงแค่ระบบใหม่เท่านั้น\n\nแต่ในส่วนโครงการที่มีการเริ่มลงไว้ที่เว็บม่วงแต่ยังประกาศไม่ครบถ้วนทุกขั้นตอน จะยังให้ลงที่เว็บเก่าแบบเดิมจนครบทุกขั้นตอน" },
            { question: "ต่ำแสน ต้องแนบกี่ไฟล์คะ ?", answer: "ระบบไม่ได้มีการจำกัดจำนวนไฟล์ค่ะ สามารถแนบได้ไม่จำกัด" },
            { question: "หมายความว่า 1 งาน ลง2เว็บหรือคะ", answer: "การลงคู่ขนาน 2 ระบบ จะให้ทำในช่วงเดือน พฤษภาคม และ มิถุนายน เพื่อให้ผู้ใช้งานได้ทดลองใช้และติดตาม feedback ต่างๆ และเมื่อถึงวันที่ 1 กค. เราจะทำการปิดระบบเดิม และใช้เพียงแค่ระบบใหม่เท่านั้น\n\nแต่ในส่วนโครงการที่มีการเริ่มลงไว้ที่เว็บม่วงแต่ยังประกาศไม่ครบถ้วนทุกขั้นตอน จะยังให้ลงที่เว็บเก่าแบบเดิมจนครบทุกขั้นตอน" },
            { question: "รหัส Vendor คือ เลขผู้เสียภาษีของร้านที่ไปซื้อใช่มั่ยครับ", answer: "รหัส Vendor จะเป็นรหัส Vendor แบบในระบบ SAP ค่ะ แต่สำหรับเลขประจำตัวผู้เสียภาษีจะเป็นอีกฟิลด์ข้อมูลนึงค่ะ" },
            { question: "ลองทดลองกันเองก่อนได้ไหมคะ ลงตัวแล้วค่อยเอามาให้หน้างานใช้ ลงที่เดวมันเยอะมากอยู่แล้วนะคะ", answer: "ต้องรบกวนใช้ทั้งสองระบบด้วยนะครับ โดยเริ่มจากโครงการที่ยังไม่ได้ลงประกาศในระบบเดิม" },
            { question: "เริ่มใช้เมื่อไหร่จะแจ้งให้ทราบอีกครั้งใช่ไหมคะ", answer: "การลงคู่ขนาน 2 ระบบ จะให้ทำในช่วงเดือน พฤษภาคม และ มิถุนายน เพื่อให้ผู้ใช้งานได้ทดลองใช้และติดตาม feedback ต่างๆ และเมื่อถึงวันที่ 1 กค. เราจะทำการปิดระบบเดิม และใช้เพียงแค่ระบบใหม่เท่านั้น\n\nแต่ในส่วนโครงการที่มีการเริ่มลงไว้ที่เว็บม่วงแต่ยังประกาศไม่ครบถ้วนทุกขั้นตอน จะยังให้ลงที่เว็บเก่าแบบเดิมจนครบทุกขั้นตอน" },
            { question: "เลขประจำตัวเว็บม่วง ใส่ 0 นะคะทางเขตแจ้งว่าไม่ให้เปิดเผยข้อมูล", answer: "สามารถกรอกเป็น 0 ได้ค่ะ" },
            { question: "คือบันทึกข้อมูลทาง e-gp ตามกฎหมายปกติ แล้วนำมาบันทึกใน e-procurement เพิ่มอีกใช่หรือไม่คะ", answer: "ใช่ค่ะ ซึ่งจะเป็นในลักษณะเดียวกันกับที่เคยทำในเว็บม่วงเลยค่ะ (https://bidding.pea.co.th/)" },
            { question: "ไฟล์ excel ยังคงมีเลขประจำตัวประชาชนด้วยไหมคะ", answer: "ไม่มีการแสดงเลขประจำตัวประชาชนในแบบฟอร์มอยู่แล้วค่ะ" },
            { question: "รบกวนช่วยตรวจสอบแบบฟอร์มรายไตรมาส ตาม ว.509 ด้วยค่ะ เหมือนรูปแบบไม่ถูกต้องค่ะ ช่องเอกสารอ้างอิง", answer: "ดำเนินการปรับให้เรียบร้อยแล้วนะคะ" },
            { question: "อยากให้ระบบลงตัวก่อน แล้วเริ่มใช้เมื่อ ไหร่ ก็ใช้แค่ระบบเดียวไปเลย ไม่ต้องให้หน้างานทำคู่ขนาน เนื่องจากหน้างานภาระงานอย่างอื่นมีเยอะครับ", answer: "ต้องรบกวนใช้ทั้งสองระบบด้วยนะครับ โดยเริ่มจากโครงการที่ยังไม่ได้ลงประกาศในระบบเดิม" },
            { question: "ข้อ.7บันทึกผลการจัดซื้อจัดจ้างวงเินต่ำกว่าแสน มีให้เลือกรายงานแบบรายเดือนด้วยมั้ยคะ ทางเขตยังมีให้ส่งข้อมูล แบบรายเดือนด้วยคะ", answer: "เมื่อมีการจัดซื้อในแต่ละครั้งสามารถบันทึกข้อมูลเข้าระบบได้ และสามารถดูรายงาน สขร.1 แบบต่ำกว่า 1 แสน ได้" },
            { question: "การบันทึกข้อมูลวงเงินต่ำแสน ในช่องที่ระบุวันที่อนุมัติ และเลขที่สัญญาข้อตกลง จะระบุยังไงคะ ตย.เช่น อนุมัติ 29 เม.ย. ใบสั่งซื้อหรือข้อตกลงออกวันที่ 2 พ.ค. ตอนขั้นตอนดึงรายงานประจำเดือนข้อมูลจะรายงานเดือนไหนคะ", answer: "เดือน พ.ค. คับ เดี๋ยวจะระบุข้อความให้ชัดเจนมากยิ่งขึ้นครับ" },
            { question: "เอกสารแนบไฟล์ excel ที่ดึงจากระบบจัดซื้อจัดจ้างไฟฟ้า ต้องมีเลขผู้เสียภาษีอยู่ไหมครับ เพราะตอบแนบไฟล์ excel ให้กรมบัญชีกลาง ต้องมีเลขบัตรประชาชนอยู่นิครับ", answer: "ณ ปัจจุบัน เรายึดแบบฟอร์มตาม ว 509 ซึ่งจะไม่มีการแสดงเลขประจำตัวผู้เสียภาษี" },
            { question: "ลายเซ็นดึงมาจากไหนครับ", answer: "มาจากการจัดการข้อมูลส่วนบุคคลที่บังคับกรอกตั้งแต่ครั้งแรกที่ login เข้าสู่ระบบ" },
            { question: "ลายเซ็นต้องดึงจากภาพอย่างเดียวหรอครับ หรือสามารถเซ็นบน tablet ได้ครับ", answer: "ณ ปัจจุบันระบบจะใช้จากภาพลายเซ็นที่แนบไว้ในข้อมูลบุคลากรค่ะ สำหรับในอนาคตถ้ามีการปรับเพิ่มเติมจะแจ้งให้ทราบอีกครั้งนะคะ" },
            { question: "ขอดูหน้ารายงานด้วยคะ เช่น รายงานประกาศผลผู้ชนะ รายงานประกาศรายไตรมาส คะ", answer: "มีการเปิดให้ดูเพิ่มเติมในวันที่ประชุมเรียบร้อยแล้ว" },
            { question: "ตัวข้อ 2 โครงการหน้า การประกาศจัดซื้อจัดจ้าง ดึงมาจากหน้าการจัดซื้อจัดจ้างได้ไหมคะ (เกินแสน)", answer: "กรณีหมายถึงการดึงข้อมูลโครงการมาจากระบบ e-gp ปัจจุบันยังไม่ได้มีการดึงนะคะ แต่สามารถดึงข้อมูลมาจากแผนการจัดซื้อจัดจ้างที่กรอกไว้ก่อนหน้าได้ค่ะ" },
            { question: "รบกวนยกตัวอย่างกรณีไม่มีรหัสพัสดุ หรือเป็นการจัดจ้างแรงงานด้วยค่ะ", answer: "ได้มีการยกตัวอย่างในที่ประชุมเรียบร้อยแล้ว" },
            { question: "ข้อมูลจัดซื้อเดิมที่บันทึกในเว็ปม่วง จะถูกดึงเข้ามาในเว็ปใหม่ด้วยมั้ยค่ะ", answer: "ไม่ได้ดึงมาค่ะ" },
            { question: "email ต้องแก้ทุกครั้งเลยไหมครับ ไม่บันทึกใช่ไหมครับ", answer: "ณ ปัจจุบันจะเป็นการกรอกอีเมลตามประกาศแต่ละฉบับเลยค่ะ แต่ถ้ามีข้อสรุปเพิ่มเติมในภายหลังให้เก็บเป็น Master หรือดึงเพิ่มเติมจากฐานข้อมูล อาจจะมีปรับเพิ่มอีกครั้งนะคะ" },
            { question: "การขอราคาตลาด เป็น case มากกว่า100000 ใช่ไหมคะ", answer: "สามารถขอราคาตลาดได้ทุกวงเงินเลยค่ะ" },
            { question: "ขอดูหน้ารายงานแสดงรายการ เช่นเรื่องประกาศผู้ชนะ/ ประกาศผู้ชนะไตรมาส ด้วยคะ", answer: "มีการเปิดให้ดูเพิ่มเติมในวันที่ประชุมเรียบร้อยแล้ว" },
            { question: "ข้อ 6 ผลการพิจารณา หน้าแรกมีคอลัมภ์แสดงเลขที่โครงการด้วยไหมคะ ดูไม่ทันคะ", answer: "มีค่ะ" },
            { question: "สขร.1 มีตัวเลือก ไฟฟ้าหน้างาน ให้เลือกไหมค่ะ เข้าไปลองเล่นมีเพียงสายงาน", answer: "ตอนนี้ยังไม่มี กำลังพัฒนาให้คครับ" },
            { question: "หน้างานย่อยการไฟฟ้าถึงจังหวัดมั้ยคะ", answer: "ไม่เข้าใจในคำถาม" },
            { question: "เหมือนสายงาน กต ก็จะมีฝ่ายที่ชื่อเหมือนกัน เช่น ฝสบ.(ต1) ฝสบ.(ต2) ไม่แน่ใจว่าตอนออกรายงานจะออกมารวมกันเลยใช่มั้ยคะ", answer: "ตอนนี้กำลังพัฒนาให้ไม่รวมกันคับ" },
            { question: "มีเบอร์ติดต่อ ในการสอบถามรึป่าวคับ", answer: "สายด่วน: 5314-5317 , 5324-5327" },
            { question: "โปรแกรมนี้ คือใช้ ทั้งแต่ ซื้อ/จ้างของ 1 บาท เลยช่มั้ยคะ", answer: "เงื่อนไขเหมือนกับการคีย์ข้อมูลในเว็บม่วงเลยค่ะ" },
            { question: "ทดลองใช้งานได้ตั้งแต่ 5 พ.ค.68 ใช่ไหมคะ", answer: "วันอังคารที่ 6 พ.ค. 68" },
            { question: "ขอตัวอย่าง การซื้อแต่ละประเภท ตามที่หน้างาน ใช้บ่อยๆ หน่อยค่ะ", answer: "สามารถศึกษาคู่มือการใช้งานได้ (อยู่แท็บด้านบนของระบบ) ทั้งนี้เมื่อมีปัญหาการใช้งาน สามารถแจ้งได้ที่มุมด้านขวาล่างของระบบครับ" },
            { question: "วันที่ 6 ลองใช้งานโดยใช้ข้อมูลเดิมที่เปิดบิดไปแล้วได่ไหมคะ", answer: "ให้ลงข้อมูลโครงการใหม่ที่เริ่มตั้งแต่แผนการจัดซื้อจัดจ้างหรือโครงการ" },
            { question: "วีดิโอตรงไหนนะคะ ฟังไม่ถนัดค่ะ", answer: "เดี๋ยวจะแจ้งเวียนให้ทราบครับ" },
            { question: "ฝากคำถมไว้หน่อยนะคะ วันที่ 6 ถ้าเริ่มใช้งาน หากงานนั้นผ่านขั้นตอนประกาศแผนไปแล้ว ให้ใช้ระบบเดิมใช่ไหมคะ", answer: "ใช่ค่ะ" }
        ];

        const chatBox = document.getElementById('chat-box');
        const searchInput = document.getElementById('search-input');

        // Variables for search functionality
        let currentSearchTerm = '';
        let currentMatches = [];
        let currentMatchIndex = -1;
        let messageElements = [];

        // Function to append messages to the chat box
        function appendMessage(sender, message) {
            const messageElement = document.createElement('div');
            messageElement.className = `flex ${sender === 'user' ? 'justify-end' : 'justify-start'} mb-2`;
            const bubbleClass = sender === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-800';
            messageElement.innerHTML = `<div class="${bubbleClass} rounded-lg px-4 py-2 max-w-xs">${message}</div>`;
            chatBox.appendChild(messageElement);
            chatBox.scrollTop = chatBox.scrollHeight;
            messageElements.push({element: messageElement, text: message});
            return messageElement;
        }

        // Display all Q&A data in the chat box
        qaData.forEach(qa => {
            appendMessage('user', qa.question);
            appendMessage('bot', qa.answer);
        });

        // Function to highlight text in messages
        function highlightText(searchTerm) {
            if (!searchTerm) return;

            // Clear previous highlights
            clearHighlights();

            currentSearchTerm = searchTerm;
            currentMatches = [];
            currentMatchIndex = -1;

            // Case insensitive search
            const regex = new RegExp(escapeRegExp(searchTerm), 'gi');

            messageElements.forEach((item, messageIndex) => {
                const { element, text } = item;
                const messageDiv = element.querySelector('div');

                if (!messageDiv) return;

                // Replace text with highlighted version
                let newHtml = text.replace(regex, (match) => {
                    currentMatches.push({ messageIndex, match });
                    return `<span class="highlight">${match}</span>`;
                });

                messageDiv.innerHTML = newHtml;
            });

            // Find next match if any found
            if (currentMatches.length > 0) {
                findNextMatch();
            }
        }

        // Helper function to escape special regex characters
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // Function to clear all highlights
        function clearHighlights() {
            messageElements.forEach(item => {
                const { element, text } = item;
                const messageDiv = element.querySelector('div');
                if (messageDiv) {
                    // Reset to original text
                    const bubbleClass = element.className.includes('justify-end') ?
                        'bg-blue-500 text-white' : 'bg-gray-300 text-gray-800';
                    messageDiv.className = `${bubbleClass} rounded-lg px-4 py-2 max-w-xs`;
                    messageDiv.innerHTML = text;
                }
            });

            currentMatches = [];
            currentMatchIndex = -1;
            currentSearchTerm = '';
        }

        // Function to find and scroll to next match
        function findNextMatch() {
            if (currentMatches.length === 0) return;

            // Move to next match
            currentMatchIndex = (currentMatchIndex + 1) % currentMatches.length;
            const match = currentMatches[currentMatchIndex];

            // Get the element containing the match
            const messageElement = messageElements[match.messageIndex].element;

            // Scroll to the element
            messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // Event listener for search input
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.trim();
            if (searchTerm) {
                highlightText(searchTerm);
            } else {
                clearHighlights();
            }
        });

        // Event listener for Enter key to find next match
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && currentMatches.length > 0) {
                findNextMatch();
            }
        });

        // Event listener for blur to clear highlights when not focused
        searchInput.addEventListener('blur', () => {
            if (!searchInput.contains(document.activeElement)) {
                clearHighlights();
            }
        });

        // Re-focus on search input when clicking on it
        searchInput.addEventListener('focus', () => {
            if (searchInput.value.trim()) {
                highlightText(searchInput.value.trim());
            }
        });

    </script>
</body>
</html>