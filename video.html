﻿<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]>      <html class="no-js"> <!--<![endif]-->
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>vidio for study</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="">
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Prompt&display=swap');

            *{
                margin: 0;
                padding:0;
                font-family: 'Prompt', sans-serif;
            }

           body{
                background: rgb(174,204,238);
                background: radial-gradient(circle, rgba(174,204,238,1) 0%, rgba(148,233,219,1) 100%);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
            }
            .video{
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 5vw;
            }
            .counter{
                display: flex;
                flex-direction: column;
                align-items: center; 
                margin: 5vw;
                border: 2px solid #000;
                padding: 1vw;
            }
            h1{
                margin-bottom: 2vw;
                text-align: center;
                font-size: 2.3vw;
            }
            h2{
                margin: 2vw 0 3vw 0;
                font-size: 2vw;
            }
            h3{
                font-size: 1.6vw;
            }
            p{
                font-size: 1.5vw;
                margin: 1vw 0 1vw 0;
            }
        </style> 
    </head>
    <body>
        <!--[if lt IE 7]>
            <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="#">upgrade your browser</a> to improve your experience.</p>
        <![endif]-->
                

        <div class="video">
            <h1>อบรมเชิงปฏิบัติการจัดซื้อจัดจ้างภาครัฐ ประจำปี 2564 <br> วันที่ 26 พฤศจิกายน 2564</h1>
            <video width="40%" controls controlsList="nodownload" oncontextmenu="return false;" id="my-video-player" src="video/อบรมเชิงปฏิบัติการ จัดซื้อจัดจ้างภาครัฐ ประจำปี 2564-20211126 0200-1.mp4"></video>
            <h2>อบรมสัมนาช่วงเช้า</h2>
            <video width="40%" controls controlsList="nodownload" oncontextmenu="return false;" id="my-video-player" src="video/อบรมเชิงปฏิบัติการ จัดซื้อจัดจ้างภาครัฐ ประจำปี 2564-20211126 0559-2.mp4"></video>
            <h2>อบรมสัมนาช่วงบ่าย</h2>
        </div>
        <!-- <div class="counter">
            <p>มีผู้เข้าชมคลิปวีดีโอ</p>
            <h3 id="counter"></h3>
            <p>ครั้ง</p>
        </div> -->


        <!-- <script>
        // Get the counter element
        const counterElement = document.getElementById('counter');

        // Retrieve the counter value from the URL query parameter
        let counter = getCounterFromURL();

        // Update the counter display
        function updateCounter() {
            counterElement.textContent = counter;
        }

        // Increment the counter
        function incrementCounter() {
            counter++;
            updateCounter();
        // Update the URL with the new counter value
            updateURL(counter);
        }

        // Function to retrieve the counter value from the URL query parameter
        function getCounterFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const counterValue = urlParams.get('counter');
        return counterValue ? parseInt(counterValue) : 0;
       }

      // Function to update the URL with the new counter value
    function updateURL(counter) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('counter', counter);
    const newURL = window.location.pathname + '?' + urlParams.toString();
    window.history.replaceState(null, '', newURL);
    }

     // Call the incrementCounter function initially
     incrementCounter();

           
        </script> -->
    </body>
</html>