<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]>      <html class="no-js"> <!--<![endif]-->
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>วิดีโอ</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;600&display=swap");

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Prompt", sans-serif;
      }

      body {
        background: linear-gradient(120deg, #74b9ff, #81ecec);
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }
      .video {
        border: 1px solid #ccc;
        border-radius: 10px;
        padding: clamp(1rem, 3vw, 2rem);
        background-color: #eeeeee;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 1200px;
        width: 100%;
        margin: 0 auto;
        overflow: hidden;
      }
    
      video {
        border-radius: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: clamp(10px, 1vw, 20px) 0;
        width: 80%;
        max-width: 85%;
        height: auto;
        display: block;
        margin-left: auto;
        margin-right: auto;
      }

      h1 {
        margin-bottom: 1vw;
        text-align: center;
        font-size: clamp(16px, 1.5vw, 24px);
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        line-height: 1.4;
        padding: 0 10px;
      }

      /* Media Queries for better responsiveness */
      @media screen and (max-width: 768px) {
        body {
          padding: 10px;
        }

        .video {
          padding: 1rem;
          max-width: 100%;
        }

        h1 {
          font-size: clamp(14px, 4vw, 20px);
          margin-bottom: 15px;
          padding: 0 5px;
        }

        video {
          max-width: 95%;
          border-radius: 15px;
        }
      }

      @media screen and (max-width: 480px) {
        .video {
          padding: 0.5rem;
          border-radius: 8px;
        }

        h1 {
          font-size: clamp(12px, 5vw, 18px);
          margin-bottom: 10px;
        }

        video {
          max-width: 100%;
          border-radius: 10px;
        }
      }

    </style>
  </head>
  <body>
    <!--[if lt IE 7]>
      <p class="browsehappy">
        You are using an <strong>outdated</strong> browser. Please
        <a href="#">upgrade your browser</a> to improve your experience.
      </p>
    <![endif]-->

    <div class="video">
      <h1>
        “Workshop ขั้นตอนการบันทึกข้อมูลในการประกาศการจัดซื้อ/จัดจ้าง (PEA e-Procurment)
         <br />เมื่อวันที่ 27 มิถุนายน 2568
      </h1>
      <video
        controls
        controlsList="download"
        oncontextmenu="return false;"
        id="my-video-player"
        src="video/Trainning-eprocurement.mp4"
      ></video>
    </div>
  </body>
</html>
